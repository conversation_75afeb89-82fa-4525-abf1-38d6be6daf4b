import { useQuery } from '@tanstack/react-query'

import { createAPI } from '@/api'

const api = createAPI()

// Функция для получения списка компаний
const getCompanies = () => {
  return api.post('/api/companies', {})
}

// Функция для поиска компаний по названию
const searchCompanies = (name) => {
  return api.post('/api/companies', { name })
}

// Ключ запроса для React Query
export const GET_COMPANIES_KEY = 'companies'
export const SEARCH_COMPANIES_KEY = 'searchCompanies'

// Хук React Query для получения списка компаний
export const useGetCompanies = () => {
  return useQuery({
    queryKey: [GET_COMPANIES_KEY],
    queryFn: () => getCompanies(),
    staleTime: 5 * 60 * 1000, // 5 минут
    refetchOnWindowFocus: false,
  })
}

// Хук React Query для поиска компаний по названию
export const useSearchCompanies = (name, enabled = true) => {
  return useQuery({
    queryKey: [SEARCH_COMPANIES_KEY, name],
    queryFn: () => searchCompanies(name),
    enabled: enabled && !!name && name.trim().length > 0,
    staleTime: 2 * 60 * 1000, // 2 минуты
    refetchOnWindowFocus: false,
  })
}
