import { useState, useEffect } from 'react'

/**
 * Хук для создания задержки при вводе
 * @param {any} value - значение для задержки
 * @param {number} delay - задержка в миллисекундах
 * @returns {any} - значение с задержкой
 */
export const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}
