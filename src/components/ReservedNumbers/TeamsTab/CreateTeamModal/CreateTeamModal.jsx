import { useState } from 'react'
import { useSelector } from 'react-redux'

import CustomSelect from '@/components/Calendar/Select/Select'
import CompanySearchSelect from '@/components/CompanySearchSelect/CompanySearchSelect'
import Input from '@/components/Forms/Input/Input'
import UniversalPopup from '@/components/Popups/UniversalPopup/UniversalPopup'

import { useGetFormats } from '@/features/formats/api/getFormats'
import { useCreateTeam } from '@/features/teams/api/createTeam'
import { getThemeMemReg } from '@/reducer/theme/selectors'

import styles from './CreateTeamModal.module.scss'

const CreateTeamModal = ({ onClose, eventCityId }) => {
  const theme = useSelector((state) => getThemeMemReg(state))

  const [formData, setFormData] = useState({
    name: '',
    number: '',
    company: null,
    format: null,
  })

  const [errors, setErrors] = useState({})

  // API хуки
  const { data: formatsResponse, isLoading: isLoadingFormats } = useGetFormats(eventCityId)
  const { mutate: createTeamMutation, isPending: isCreating } = useCreateTeam()

  const formats = formatsResponse?.data?.values || []

  // Преобразование данных для селектов
  const formatOptions = formats.map((format) => ({
    value: format.public_id,
    label: format.name,
  }))

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))

    // Очищаем ошибку для поля при изменении
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: '',
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Введите название команды'
    }

    if (!formData.number || formData.number <= 0) {
      newErrors.number = 'Введите корректный номер команды'
    }

    if (!formData.company) {
      newErrors.company = 'Выберите компанию'
    }

    if (!formData.format) {
      newErrors.format = 'Выберите формат'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    const teamData = {
      format: {
        public_id: formData.format.value,
      },
      number: parseInt(formData.number, 10),
      company: {
        public_id: formData.company.value,
      },
      name: formData.name,
      city: {
        id: eventCityId,
      },
    }

    createTeamMutation(teamData, {
      onSuccess: () => {
        onClose()
      },
    })
  }

  const handleClose = () => {
    onClose()
  }

  return (
    <UniversalPopup>
      <div className={`${styles.wrapper} ${theme ? 'mainWhite' : ''}`}>
        <button className={styles.closeBtn} onClick={handleClose} type="button" aria-label="Закрыть модальное окно" />

        <h2 className={styles.title}>Создать команду</h2>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.field}>
            <Input
              outsideStyle={styles}
              label="Название команды"
              value={formData.name}
              handleFieldChange={(e) => handleInputChange('name', e.target.value)}
              error={errors.name}
              placeholder="Введите название команды"
            />
          </div>

          <div className={styles.field}>
            <Input
              outsideStyle={styles}
              label="Номер команды"
              type="number"
              value={formData.number}
              handleFieldChange={(e) => handleInputChange('number', e.target.value)}
              error={errors.number}
              placeholder="Введите номер команды"
              min="1"
            />
          </div>

          <div className={styles.field}>
            <CompanySearchSelect
              value={formData.company}
              onChange={(selectedOption) => handleInputChange('company', selectedOption)}
              error={errors.company}
              placeholder="Начните вводить название компании..."
              title="Компания"
              styles={styles}
              prefix="create-team-company-select"
            />
          </div>

          <div className={styles.field}>
            <CustomSelect
              title="Формат"
              options={formatOptions}
              value={formData.format}
              handleSelectChange={(selectedOption) => handleInputChange('format', selectedOption)}
              error={errors.format}
              placeholder="Выберите формат"
              disabled={isLoadingFormats}
              styles={styles}
              prefix="create-team-select"
            />
          </div>

          <div className={styles.buttons}>
            <button type="button" className={styles.cancelBtn} onClick={handleClose} disabled={isCreating}>
              Отмена
            </button>

            <button type="submit" className={styles.submitBtn} disabled={isCreating}>
              {isCreating ? 'Создание...' : 'Создать команду'}
            </button>
          </div>
        </form>
      </div>
    </UniversalPopup>
  )
}

export default CreateTeamModal
