@import '../../../Scss/Mixins.scss';

.wrapper {
  position: relative;
  max-width: 40rem;
  width: 100%;
  padding: 2.5rem;
  background-color: #151515;
  border-radius: 1rem;
  border: 1px solid #2a2b2e;
  color: white;
}

.closeBtn {
  position: absolute;
  top: 0.625rem;
  right: 0.625rem;
  width: 1.875rem;
  height: 1.875rem;
  padding: 0;
  cursor: pointer;
  background-color: transparent;
  border: none;

  @include pseudo-cross-btn($r: 45deg);
}

.title {
  font-family: 'DIN Pro';
  font-size: 1.5rem;
  font-weight: 900;
  line-height: 1.222;
  margin-bottom: 2rem;
  text-align: center;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.field {
  // display: flex;
  // flex-direction: column;
}

.inputGroup {
  max-width: 100%;
}

.buttons {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  justify-content: flex-end;
}

.cancelBtn,
.submitBtn {
  @include btn45;
  padding: 0.75rem 1.5rem;
  border-radius: 0.625rem;
  min-width: 8rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.cancelBtn {
  background-color: #2a2b2e;
  border: 1px solid #4b4b4b;
  color: white;

  &:hover:not(:disabled) {
    background-color: #3a3b3e;
  }
}

.submitBtn {
  @include gradientViolet;
  border: none;
  color: white;

  &:hover:not(:disabled) {
    opacity: 0.9;
  }
}

// Стили для селектов
.selector {
  position: relative;
  background-color: #2a2b2e;
  border: 1px solid #4b4b4b;
  border-radius: 0.5rem;
  transition: border-color 0.3s ease;

  &Error {
    border-color: #ff6b6b;
  }

  &Disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.filter {
  position: absolute;
  top: -0.5rem;
  left: 1rem;
  background-color: #151515;
  padding: 0 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  z-index: 1;
}

.inputWrap {
  position: relative;
}

.selectIcon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1rem;
  height: 1rem;
  z-index: 2;

  &Error {
    color: #ff6b6b;
  }
}

.messageError {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #ff6b6b;
  font-weight: 400;
}

// Светлая тема
:global(.mainWhite) {
  .wrapper {
    background-color: #ffffff;
    border-color: #e0e0e0;
    color: #111113;
  }

  .title {
    color: #111113;
  }

  .selector {
    background-color: #f5f5f5;
    border-color: #d0d0d0;

    &Error {
      border-color: #d32f2f;
    }
  }

  .filter {
    background-color: #ffffff;
  }

  .cancelBtn {
    background-color: #f5f5f5;
    border-color: #d0d0d0;
    color: #111113;

    &:hover:not(:disabled) {
      background-color: #e5e5e5;
    }
  }

  .messageError {
    color: #d32f2f;
  }

  .selectIconError {
    color: #d32f2f;
  }
}

@media (max-width: $mobileWidth) {
  .wrapper {
    padding: 1.5rem;
    max-width: 90vw;
  }

  .title {
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
  }

  .form {
    gap: 1rem;
  }

  .buttons {
    flex-direction: column;
    gap: 0.75rem;
  }

  .cancelBtn,
  .submitBtn {
    min-width: auto;
    width: 100%;
  }
}
