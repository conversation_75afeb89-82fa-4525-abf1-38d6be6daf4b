import { useState, useMemo } from 'react'
import Select, { components } from 'react-select'

import { customStyles, customTheme } from '@/components/Calendar/Select/custom'

import { useSearchCompanies } from '@/features/companies/api/getCompanies'
import { useDebounce } from '@/hooks/useDebounce'
import ArrowIcon from '@/images/svg/arrow-select-icon.svg?react'

// Кастомные стили для компонента поиска компаний
const companySearchStyles = {
  ...customStyles,
  menu: (provided) => ({
    ...provided,
    width: '100%',
    color: 'white',
    padding: '1.875rem 1.25rem 1.25rem 0.625rem',
    backgroundColor: '#151515',
    zIndex: 9999, // Высокий z-index для отображения поверх других элементов
    borderRadius: '5px',
  }),
  menuPortal: (provided) => ({
    ...provided,
    zIndex: 9999,
  }),
  input: (provided) => ({
    ...provided,
    color: 'white',
    caretColor: 'white',
  }),
}

const CompanySearchSelect = ({
  value,
  onChange,
  error = '',
  disabled = false,
  styles,
  placeholder = 'Начните вводить название компании...',
  title = 'Компания',
  prefix = 'company-search-select',
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const debouncedSearchTerm = useDebounce(searchTerm, 500) // Задержка 500мс

  // Поиск компаний только если есть введенный текст
  const { data: companiesResponse, isLoading } = useSearchCompanies(
    debouncedSearchTerm,
    debouncedSearchTerm.trim().length > 0
  )

  // Преобразование данных для селекта
  const options = useMemo(() => {
    if (!companiesResponse?.data?.values) return []

    // Ограничиваем до 10 результатов
    return companiesResponse.data.values.slice(0, 10).map((company) => ({
      value: company.public_id,
      label: company.name,
    }))
  }, [companiesResponse])

  // Кастомные компоненты
  const ControlComponent = (props) => (
    <div
      className={`${styles.selector} ${error.length ? styles.selectorError : ''} ${
        disabled ? styles.selectorDisabled : ''
      }`}
    >
      <b className={styles.filter}>{title}</b>
      <components.Control {...props} />
      {error.length !== 0 ? <p className={styles.messageError}>{error}</p> : null}
    </div>
  )

  const DropdownIndicator = (props) => {
    return (
      <components.DropdownIndicator {...props}>
        <ArrowIcon />
      </components.DropdownIndicator>
    )
  }

  const LoadingMessage = () => <div style={{ padding: '8px 12px', color: '#666' }}>Поиск компаний...</div>

  const NoOptionsMessage = ({ inputValue: currentInputValue }) => (
    <div style={{ padding: '8px 12px', color: '#666' }}>
      {!currentInputValue || currentInputValue.trim().length === 0
        ? 'Начните вводить название компании'
        : 'Компании не найдены'}
    </div>
  )

  const handleInputChange = (newValue, actionMeta) => {
    if (actionMeta.action === 'input-change') {
      setSearchTerm(newValue)
    }
  }

  const handleChange = (selectedOption) => {
    onChange(selectedOption)
  }

  return (
    <div className={styles.inputWrap}>
      <Select
        value={value}
        onChange={handleChange}
        onInputChange={handleInputChange}
        className={`${prefix}-container`}
        classNamePrefix={prefix}
        isSearchable
        components={{
          Control: ControlComponent,
          DropdownIndicator,
          LoadingMessage,
          NoOptionsMessage,
        }}
        options={options}
        placeholder={placeholder}
        styles={companySearchStyles}
        theme={customTheme}
        isLoading={isLoading}
        isDisabled={disabled}
        filterOption={null} // Отключаем встроенную фильтрацию
        noOptionsMessage={NoOptionsMessage}
        loadingMessage={LoadingMessage}
        isClearable
        menuPortalTarget={document.body} // Рендерим меню в body для решения проблем с z-index
        menuPosition="fixed" // Фиксированное позиционирование
      />
    </div>
  )
}

export default CompanySearchSelect
