import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { vi } from 'vitest'

import CompanySearchSelect from './CompanySearchSelect'

// Мокаем API хук
vi.mock('@/features/companies/api/getCompanies', () => ({
  useSearchCompanies: vi.fn(),
}))

// Мокаем хук debounce
vi.mock('@/hooks/useDebounce', () => ({
  useDebounce: vi.fn((value) => value), // Возвращаем значение без задержки для тестов
}))

const mockStyles = {
  inputWrap: 'inputWrap',
  selector: 'selector',
  selectorError: 'selectorError',
  selectorDisabled: 'selectorDisabled',
  filter: 'filter',
  messageError: 'messageError',
}

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  return ({ children }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('CompanySearchSelect', () => {
  const mockOnChange = vi.fn()
  
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('должен отображать placeholder при первой загрузке', () => {
    const { useSearchCompanies } = require('@/features/companies/api/getCompanies')
    useSearchCompanies.mockReturnValue({
      data: null,
      isLoading: false,
    })

    render(
      <CompanySearchSelect
        value={null}
        onChange={mockOnChange}
        styles={mockStyles}
        placeholder="Начните вводить название компании..."
      />,
      { wrapper: createWrapper() }
    )

    expect(screen.getByText('Начните вводить название компании...')).toBeInTheDocument()
  })

  it('должен показывать индикатор загрузки при поиске', async () => {
    const { useSearchCompanies } = require('@/features/companies/api/getCompanies')
    useSearchCompanies.mockReturnValue({
      data: null,
      isLoading: true,
    })

    render(
      <CompanySearchSelect
        value={null}
        onChange={mockOnChange}
        styles={mockStyles}
      />,
      { wrapper: createWrapper() }
    )

    // Вводим текст для поиска
    const input = screen.getByRole('combobox')
    fireEvent.change(input, { target: { value: 'test' } })

    await waitFor(() => {
      expect(screen.getByText('Поиск компаний...')).toBeInTheDocument()
    })
  })

  it('должен отображать найденные компании', async () => {
    const { useSearchCompanies } = require('@/features/companies/api/getCompanies')
    useSearchCompanies.mockReturnValue({
      data: {
        data: {
          values: [
            { public_id: '1', name: 'Компания 1' },
            { public_id: '2', name: 'Компания 2' },
          ],
        },
      },
      isLoading: false,
    })

    render(
      <CompanySearchSelect
        value={null}
        onChange={mockOnChange}
        styles={mockStyles}
      />,
      { wrapper: createWrapper() }
    )

    // Вводим текст для поиска
    const input = screen.getByRole('combobox')
    fireEvent.change(input, { target: { value: 'Компания' } })

    await waitFor(() => {
      expect(screen.getByText('Компания 1')).toBeInTheDocument()
      expect(screen.getByText('Компания 2')).toBeInTheDocument()
    })
  })
})
